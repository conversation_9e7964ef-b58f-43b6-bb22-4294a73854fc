using API.Filters.CustomAuthorization;
using API.Middlewares;
using ESG;
using ESG.Interfaces;
using ESG.Services;
using Exports.Services;
using Imports.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Report.MonthlyReport;
using System.Reflection;
using TokenManager.Service.Interfaces;
using TokenManager.Service.Model;
using Valuation;
using Valuation.DtoProfiles;
using Valuation.Interface;
using Valuation.Services;
using Workflow;
using Workflow.Interface;
using Workflow.Services;
namespace API.Extensions
{
    public static partial class ServiceExtensions
    {
        public static void AddDI(IServiceCollection services, IConfiguration Configuration)
        {
            services.AddSingleton<ICacheManager, CacheManager>();
            services.AddTransient<IValuation, ValuationService>();
            services.AddTransient<IValuationExcelService, ValuationExcelService>();
            services.AddTransient<IValuationModelDbService, ValuationModelDbService>();
            services.AddTransient<ITransactionCompsValuation, TransactionCompsValuation>();
            services.AddTransient<ITradingCompsValuation, TradingCompsValuation>();
            services.AddTransient<IImpliedEVService, ImpliedEVService>();
            services.AddTransient<IAdjustmentDetailDbService, AdjustmentDetailDbService>();
            services.AddTransient<IImpliedEVRecordDbService, ImpliedEVRecordDbService>();
            services.AddTransient<ITargetCompanyKpiDbService, TargetCompanyKpiDbService>();
            services.AddTransient<IUnselectedRecordsDbService, UnselectedRecordsDbService>();
            services.AddTransient<IEquityValueService, EquityValueService>();
            services.AddTransient<TokenManagerMiddleware>();
            services.AddTransient<ITokenManager, TokenManager.Service.TokenManager>();
            
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<IEsgKpiService, EsgKpiService>();
            services.AddTransient<IEsgExcelService, EsgExcelService>(); 
            services.AddTransient<IEsgKpiStaticInfoService, EsgKpiStaticInfoService>();
            services.AddTransient<IEsgNotificationService, EsgNotificationService>();
            services.AddTransient<IEsgKpiAuditLogService, EsgKpiAuditLogService>();
            services.AddTransient<IWorkflowEmailService, WorkflowEmailService>();
            services.AddTransient<IWorkflowPCService, WorkflowPCService>();
            services.AddTransient<IMonthlyReportService, MonthlyReportService>();
            services.AddTransient<IMonthlyReportExcelService, MonthlyReportExcelService>();
            services.AddTransient<IAuthorizationPolicyProvider, UserFeatureBasedPolicyProvider>();
            // As always, handlers must be provided for the requirements of the authorization policies
            services.AddTransient<IAuthorizationHandler, UserFeatureAuthorizeHandler>();

            var jwtSection = Configuration.GetSection("JwtOption");
            var jwtOptions = new JwtOption();
            jwtSection.Bind(jwtOptions);
            services.Configure<JwtOption>(jwtSection);
        }

        public static void AddAutoMapper(IServiceCollection services)
        {
            services.AddAutoMapper(Assembly.GetAssembly(typeof(ValuationDtoProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(DocumentInformationProfile)));
        }
       
    }
}
